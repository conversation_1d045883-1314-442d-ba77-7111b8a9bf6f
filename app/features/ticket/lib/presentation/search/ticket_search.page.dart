import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/presentation/filter/ticket_filter_button.dart';
import 'package:gp_feat_ticket/presentation/home/<USER>/ticket_list_bloc.dart';
import 'package:gp_feat_ticket/widgets/search_bar.dart';
import 'package:gp_shared/presentation/base/base.dart';

import '../../data/model/request/ticket/list/ticket_list_params.dart';
import '../../domain/entity/enums/home/<USER>';
import '../home/<USER>/ticket_search_listview.dart';
// import 'widgets/ticket_listview.dart';
// import 'bloc/ticket_list_bloc.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({
    required this.tabEntity,
    super.key,
  });

  final HomeTabEntity tabEntity;
  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TicketListBloc bloc = TicketListBloc();
  final TextEditingController textController = TextEditingController();
  late TicketListParams defaultParams;
  late TicketListParams currentFilterParams;

  Timer? _debounce;

  final ValueNotifier<bool> rxHasText = ValueNotifier(false);
  final ValueNotifier<bool> rxSearchByName = ValueNotifier(true);
  final ValueNotifier<bool> rxHasActiveFilter = ValueNotifier(false);
  bool isSubmitted = false;

  late GPBaseListViewParamsV2 defaultListParams = GPBaseListViewParamsV2(
    listBloc: bloc,
    inputData: defaultParams,
    needLoadDataWhenInitial: false,
    onRefresh: () {
      loadData();
    },
  );

  @override
  void initState() {
    super.initState();
    defaultParams =
        widget.tabEntity.mapTicketHomeTabsToParams(TicketHomeTabs.all);
    currentFilterParams = defaultParams;
  }

  @override
  void dispose() {
    _debounce?.cancel();
    textController.dispose();
    // bloc.close();
    rxHasText.dispose();
    rxSearchByName.dispose();
    rxHasActiveFilter.dispose();
    super.dispose();
  }

  void _onClose() {
    if (!mounted) return;
    Navigator.of(context).maybePop();
  }

  void onSearchTextChanged(String text) {
    if (_debounce?.isActive ?? false) {
      _debounce?.cancel();
    }

    _debounce = Timer(const Duration(milliseconds: 300), () {
      loadData();
    });
  }

  void loadData() {
    final text = textController.text;

    rxHasText.value = text.isNotEmpty;

    if (text.isEmpty) {
      if (rxHasActiveFilter.value) {
        load(currentFilterParams);
      }
    } else {
      final params = currentFilterParams.copyWith(
        search: rxSearchByName.value ? text : null,
        searchByCode: rxSearchByName.value ? null : text,
      );

      search(params);
    }
  }

  void search(TicketListParams params) {
    currentFilterParams = params;
    defaultListParams.inputData = currentFilterParams;

    bloc.searchData(
      searchParams: params,
      inputData: currentFilterParams,
    );
  }

  void load(TicketListParams params) {
    bloc.loadData(
      inputData: params,
    );
  }

  void _onFilterApply(TicketListParams updatedParams) {
    currentFilterParams = updatedParams;
    final bool hasFilter = updatedParams != defaultParams;
    rxHasActiveFilter.value = hasFilter;

    if (textController.text.isNotEmpty) {
      onSearchTextChanged(textController.text);
    } else {
      rxHasText.value = hasFilter;
      if (hasFilter) {
        load(updatedParams);
      }
    }
  }

  // TODO: change to enum later
  Widget _buildChip(String name, bool isSelected) {
    final radius = BorderRadius.circular(20);
    return InkWell(
      onTap: () {
        rxSearchByName.value =
            name == LocaleKeys.ticket_home_menu_tabs_search_by_name.tr;

        onSearchTextChanged(textController.text);
      },
      borderRadius: radius,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected
              ? GPColor.functionPositiveSecondary
              : GPColor.bgSecondary,
          border: Border.all(
            width: 2,
            color: isSelected
                ? GPColor.functionAccentWorkSecondary
                : GPColor.bgSecondary,
          ),
          borderRadius: radius,
        ),
        child: Text(
          name,
          style: textStyle(GPTypography.bodyMedium),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<GPBaseListBlocV2>(
      create: (context) => bloc,
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              Row(
                children: [
                  IconButton(
                    icon: SvgWidget(
                      Assets
                          .PACKAGES_GP_ASSETS_IMAGES_IC24_FILL_CHEVRON_LEFT_PNG,
                      color: GPColor.contentPrimary,
                      width: 24,
                      height: 24,
                    ),
                    onPressed: _onClose,
                  ),
                  Expanded(
                    child: TicketSearchBar(
                      textEditingController: textController,
                      autoFocus: true,
                      onTextChanged: (text) {
                        isSubmitted = false;

                        rxHasText.value = text.isNotEmpty;
                      },
                      onFieldSubmit: (text) {
                        isSubmitted = true;

                        onSearchTextChanged(text);
                      },
                    ),
                  ),
                  FilterButton(
                    defaultTicketListParams: defaultParams,
                    tabEntity: widget.tabEntity,
                    onFilterApplied: _onFilterApply,
                    homeTab: TicketHomeTabs.all,
                  ),
                  const SizedBox(width: 8),
                ],
              ),
              Divider(
                thickness: 1.5,
                color: GPColor.lineTertiary,
              ),
              ValueListenableBuilder(
                valueListenable: rxHasText,
                builder: (context, value, child) {
                  return Column(
                    children: [
                      ValueListenableBuilder(
                        valueListenable: rxSearchByName,
                        builder: (context, value, child) {
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              alignment: WrapAlignment.start,
                              spacing: 5,
                              children: [
                                const SizedBox(width: 15),
                                _buildChip(
                                  LocaleKeys
                                      .ticket_home_menu_tabs_search_by_name.tr,
                                  value,
                                ),
                                _buildChip(
                                  LocaleKeys
                                      .ticket_home_menu_tabs_search_by_code.tr,
                                  !value,
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      if (!isSubmitted) ...{
                        Center(
                          child: Container(
                            height: 132,
                            width: double.infinity,
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SvgWidget(
                                  Assets
                                      .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_MAGNIFYINGGLASS_SEARCH_PNG,
                                  color: Colors.black,
                                  width: 25,
                                  height: 25,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  LocaleKeys.ticket_search_screen_hint.tr,
                                  textAlign: TextAlign.center,
                                  style: textStyle(GPTypography.bodyMedium)
                                      ?.mergeColor(GPColor.contentSecondary),
                                ),
                              ],
                            ),
                          ),
                        )
                      },
                    ],
                  );
                },
              ),
              const SizedBox(height: 4),
              Expanded(
                child: ValueListenableBuilder(
                  valueListenable: rxHasText,
                  builder: (context, value, child) {
                    return Stack(
                      children: [
                        NotificationListener<ScrollNotification>(
                          onNotification: (scrollNotification) {
                            if (scrollNotification is ScrollStartNotification) {
                              FocusScope.of(context).unfocus();
                            }
                            return false;
                          },
                          child: GPBaseListViewWrapperV2(
                            params: defaultListParams,
                            builder: (GPBaseListViewParamsV2 params) {
                              return TicketSearchListView(
                                params: params,
                                searchStr: textController.text,
                                shrinkWrap: true,
                                // TODO(toannm): 14/04: đang thấy bloc bị dispose khi search, rồi clear text, thêm tạm widget để chống cháy.
                                onSearchEmptyWidget: const SizedBox(),
                              );
                            },
                          ),
                        ),
                        // trick tạm để tránh Bloc bị dispose khi search, rồi clear text
                        if (!isSubmitted)
                          Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: GPColor.bgPrimary,
                          )
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SearchScreenWrapper extends StatelessWidget {
  const SearchScreenWrapper({
    required this.tabEntity,
    super.key,
  });

  final HomeTabEntity tabEntity;

  @override
  Widget build(BuildContext context) {
    return SearchScreen(
      tabEntity: tabEntity,
    );
  }
}
