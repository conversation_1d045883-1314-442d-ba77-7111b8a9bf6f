import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/select_invite_options.entity.dart';
import 'package:gp_shared/widgets/member_picker/member_picker.dart';

final class TicketMemberPickerParams
    extends MemberPickerParams<SelectMemberEntity> {
  const TicketMemberPickerParams({
    required super.options,
    super.selecMemberEntity,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketMemberPickerParams &&
        other.options == options &&
        other.selecMemberEntity == selecMemberEntity;
  }

  @override
  int get hashCode {
    return Object.hash(
      options,
      selecMemberEntity,
    );
  }

  factory TicketMemberPickerParams._({
    required String title,
    required String actionButtonTitle,
    required List<SelectInviteeTabs> tabs,
    SelectMemberEntity? currentSelectMemberEntity,
    SelectInviteesOptionsMode pickerMode =
        SelectInviteesOptionsMode.selectNewWithValue,
    Function()? onBackPressed,
    int? limitPickUser,
    List<Assignee>? inputMembers,
    bool ignoreMe = false,
    SelectInviteesPickMode pickMode = SelectInviteesPickMode.pickMultiple,
    List<int>? notRemovableUserIds,
    List<int>? filterOutMemberIds,
    bool? closeOverlayWhenBack,
    bool needToCheckPressDone = true,
  }) {
    final options = SelectInviteesOptions(
      title: title,
      actionButtonTitle: actionButtonTitle,
      tabs: tabs,
      mode: pickerMode,
      onBackPressed: onBackPressed,
      sConfigs: {
        'limit_users': limitPickUser,
        'empty_text': LocaleKeys.error_empty.tr,
        'search_only_current_workspace': true,
        'need_to_check_press_done': needToCheckPressDone
      },
      onlyCurrentWorkspace: true,
      arguments: {
        'ignore_me': ignoreMe,
      },
      notRemovableUserIds: notRemovableUserIds,
      selectInviteesPickMode: pickMode,
      filterOutMemberIds: filterOutMemberIds,
      closeOverlayWhenBack: closeOverlayWhenBack,
    );

    if (inputMembers != null) {
      options.memberConfigs = {
        'input_members': ListAPIResponse(data: inputMembers),
      };
    }

    return TicketMemberPickerParams(
      options: options,
      selecMemberEntity: currentSelectMemberEntity,
    );
  }

  factory TicketMemberPickerParams.pickDepartment({
    SelectMemberEntity? currentSelectMemberEntity,
    SelectInviteesOptionsMode pickerMode =
        SelectInviteesOptionsMode.selectNewWithValue,
    Function()? onBackPressed,
  }) {
    return TicketMemberPickerParams._(
      title: LocaleKeys.ticket_create_input_department_title.tr,
      actionButtonTitle: LocaleKeys.ticket_create_input_department_done_btn.tr,
      tabs: [
        SelectInviteeTabs.department,
      ],
      currentSelectMemberEntity: currentSelectMemberEntity,
      pickerMode: pickerMode,
      onBackPressed: onBackPressed,
    );
  }

  factory TicketMemberPickerParams.pickMember({
    SelectMemberEntity? currentSelectMemberEntity,
    SelectInviteesOptionsMode pickerMode =
        SelectInviteesOptionsMode.selectNewWithValue,
    Function()? onBackPressed,
    bool? closeOverlayWhenBack,
    bool needToCheckPressDone = true,
    bool ignoreMe = false,
  }) {
    return TicketMemberPickerParams._(
      title: LocaleKeys.ticket_create_input_member_title.tr,
      actionButtonTitle: LocaleKeys.ticket_create_input_member_done_btn.tr,
      tabs: [
        SelectInviteeTabs.member,
      ],
      currentSelectMemberEntity: currentSelectMemberEntity,
      pickerMode: pickerMode,
      onBackPressed: onBackPressed,
      closeOverlayWhenBack: closeOverlayWhenBack,
      needToCheckPressDone: needToCheckPressDone,
      ignoreMe: ignoreMe,
    );
  }

  factory TicketMemberPickerParams.pickExistsMember({
    SelectMemberEntity? currentSelectMemberEntity,
    SelectInviteesOptionsMode pickerMode = SelectInviteesOptionsMode.edit,
    Function()? onBackPressed,
    int? limitPickUser,
    List<Assignee>? inputMembers,
    bool ignoreMe = false,
    SelectInviteesPickMode pickMode = SelectInviteesPickMode.pickOne,
    List<int>? notRemovableUserIds,
    String? title,
    List<int>? filterOutMemberIds,
  }) {
    return TicketMemberPickerParams._(
      title: title ?? LocaleKeys.ticket_create_input_member_title.tr,
      actionButtonTitle: LocaleKeys.ticket_create_input_member_done_btn.tr,
      tabs: [
        SelectInviteeTabs.member,
      ],
      inputMembers: inputMembers,
      limitPickUser: limitPickUser,
      currentSelectMemberEntity: currentSelectMemberEntity,
      pickerMode: pickerMode,
      onBackPressed: onBackPressed,
      ignoreMe: ignoreMe,
      pickMode: pickMode,
      notRemovableUserIds: notRemovableUserIds,
      filterOutMemberIds: filterOutMemberIds,
    );
  }
}
