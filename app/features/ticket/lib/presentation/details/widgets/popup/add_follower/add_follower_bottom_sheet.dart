import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/follower_utils.dart';
import 'package:gp_shared/domain/entity/assignee/assignee.entity.dart';

import '../../../../../domain/entity/enums/ticket/action/ticket_action.popup.dart';
import '../../../../../domain/entity/enums/ticket/action/add_follower_option.dart';

class AddFollowerBottomSheet extends StatelessWidget {
  const AddFollowerBottomSheet({
    super.key,
    required this.entity,
    this.ticketFollowers,
  });

  final TicketActionBottomSheetEntity entity;
  final List<AssigneeEntity>? ticketFollowers;

  Future<void> _onOptionTap(
      BuildContext context, AddFollowerOption option) async {
    final List<AssigneeEntity> assigneeEntities = [];
    if (option == AddFollowerOption.currentStep) {
      final List<AssigneeEntity?> nodeFollowersEntities = entity
              .ticketNodeEntity.defaultFollowers
              ?.map((e) => e.info)
              .toList() ??
          [];
      nodeFollowersEntities.removeWhere((e) => e == null);
      assigneeEntities.addAll(List<AssigneeEntity>.from(nodeFollowersEntities));
    } else {
      assigneeEntities.addAll(ticketFollowers ?? []);
    }

    final results = await FollowerUtils.pickMembers(
      assigneeEntities: assigneeEntities,
      pickerMode: SelectInviteesOptionsMode.selectNewWithValue,
      closeOverlayWhenBack: false,
    );

    if (results == null) return;

    final followerChanges =
        FollowerUtils.calculateFollowerChanges(assigneeEntities, results);

    if (followerChanges.isEmpty) return;

    final result = AddFollowerBottomSheetResult(
      option: option,
      addMembers: followerChanges.addedMembers,
      removeMembers: followerChanges.removedMembers,
    );

    Get.back(result: result);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(context),
            Divider(
              height: 1,
              thickness: 1,
              color: GPColor.bgSecondary,
            ),
            // Options
            _buildOptions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      child: Row(
        children: [
          const SizedBox(width: 48), // Space for alignment
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                LocaleKeys.ticket_details_add_follower_title.tr,
                textAlign: TextAlign.center,
                style: textStyle(GPTypography.headingMedium)?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  height: 1.5,
                  color: GPColor.contentPrimary,
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () => Get.back(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: SvgWidget(
                Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_XMARK_SVG,
                color: GPColor.contentPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(BuildContext context) {
    return Container(
      color: GPColor.bgPrimary,
      child: Column(
        children: [
          // Current step option
          _buildOptionItem(
            context,
            AddFollowerOption.currentStep,
          ),
          // Divider
          Container(
            height: 1,
            margin: const EdgeInsets.only(left: 64),
            color: GPColor.lineTertiary,
          ),

          // All steps option
          _buildOptionItem(
            context,
            AddFollowerOption.allSteps,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(BuildContext context, AddFollowerOption option) {
    return InkWell(
      onTap: () => _onOptionTap(context, option),
      child: Container(
        padding: const EdgeInsets.fromLTRB(24, 20, 8, 20),
        child: Row(
          children: [
            // Icon
            SvgWidget(
              option.iconAsset,
              width: 24,
              height: 24,
              color: GPColor.contentPrimary,
            ),

            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.displayName,
                    style: textStyle(GPTypography.headingMedium)?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    option.description,
                    style: textStyle(GPTypography.bodyLarge)?.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 16,
                      height: 1.5,
                      color: GPColor.contentSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Chevron right
            SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_CHEVRON_RIGHT_SVG,
              width: 24,
              height: 24,
              color: GPColor.contentSecondary,
            ),
          ],
        ),
      ),
    );
  }
}

/// Result class for add follower bottom sheet
class AddFollowerBottomSheetResult {
  const AddFollowerBottomSheetResult({
    required this.option,
    this.addMembers = const [],
    this.removeMembers = const [],
  });

  final AddFollowerOption option;
  final List<AssigneeEntity> addMembers;
  final List<AssigneeEntity> removeMembers;
}
