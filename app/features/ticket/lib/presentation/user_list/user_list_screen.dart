/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/presentation/base/base.dart';

import '../../domain/domain.dart';
import 'bloc/user_list_bloc.dart';
import 'widgets/user_list_view.dart';

class UserListScreen extends StatefulWidget {
  const UserListScreen({
    required this.ticketId,
    required this.nodeId,
    this.title,
    this.onUserSelected,
    super.key,
  });

  final String ticketId;
  final String nodeId;
  final String? title;
  final Function(Assignee)? onUserSelected;

  @override
  State<UserListScreen> createState() => _UserListScreenState();
}

class _UserListScreenState extends State<UserListScreen> {
  late UserListBloc bloc;
  final TextEditingController searchController = TextEditingController();
  Timer? _debounce;

  late GPBaseListViewParamsV2 listParams;

  @override
  void initState() {
    super.initState();
    bloc = UserListBloc(GetIt.I<TicketNodeAssigneesUseCase>());
    bloc.initialize(
      ticketId: widget.ticketId,
      nodeId: widget.nodeId,
    );

    listParams = GPBaseListViewParamsV2(
      listBloc: bloc,
      needLoadDataWhenInitial: true,
      onRefresh: () {
        bloc.loadData(isInitialLoad: true);
      },
    );

    searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      bloc.updateSearchQuery(searchController.text);
      bloc.loadData(isInitialLoad: true);
    });
  }

  void _onUserTap(Assignee user) {
    widget.onUserSelected?.call(user);
    Utils.back();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? 'Select User'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Utils.back(),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: 'Search users...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          // User list
          Expanded(
            child: GPBaseListViewWrapperV2(
              params: listParams,
              builder: (GPBaseListViewParamsV2 params) {
                return UserListView(
                  params: params,
                  onUserTap: _onUserTap,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
