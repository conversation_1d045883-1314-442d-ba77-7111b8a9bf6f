/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:gp_shared/presentation/base/base.dart';
import 'package:injectable/injectable.dart';

import '../../../domain/domain.dart';
import '../model/assignee_list_entity.dart';

@Injectable()
class UserListBloc extends GPBaseListBlocV2<AssigneeListEntity,
        TicketNodeAssigneesOutput, dynamic>
    implements
        BaseListBehavior<AssigneeListEntity, TicketNodeAssigneesOutput,
            dynamic> {
  UserListBloc(
    this._ticketNodeAssigneesUseCase,
  ) {
    setOnUseCaseBehavior(this);
  }

  final TicketNodeAssigneesUseCase _ticketNodeAssigneesUseCase;

  String? _ticketId;
  String? _nodeId;
  String? _searchQuery;

  void initialize({
    required String ticketId,
    required String nodeId,
  }) {
    _ticketId = ticketId;
    _nodeId = nodeId;
  }

  void updateSearchQuery(String query) {
    _searchQuery = query.trim().isEmpty ? null : query.trim();
  }

  @override
  Future<TicketNodeAssigneesOutput?> usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    if (_ticketId == null || _nodeId == null) {
      throw Exception('TicketId and NodeId must be initialized');
    }

    final input = TicketNodeAssigneesInput(
      ticketId: _ticketId!,
      nodeId: _nodeId!,
      search: _searchQuery,
      limit: 30,
    );

    return await _ticketNodeAssigneesUseCase.execute(input);
  }

  @override
  Future<TicketNodeAssigneesOutput?> usecaseOnSearchData({
    required dynamic params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    // For this implementation, search is handled by updateSearchQuery + loadData
    return usecaseOnLoadData(
      isRefreshData: isRefreshData,
      inputData: inputData,
      nextLink: nextLink,
      page: page,
    );
  }

  @override
  Future<BaseListDataLoaded<AssigneeListEntity>> emitData({
    TicketNodeAssigneesOutput? response,
    List<AssigneeListEntity>? entityData,
    required Emitter<BaseListState> emit,
    required bool isInitialLoad,
  }) async {
    final assignees = response?.assignees ?? [];
    final data = entityData ??
        assignees
            .map((assignee) => AssigneeListEntity(assignee: assignee))
            .toList();

    final state = BaseListDataLoaded<AssigneeListEntity>(
      isInitialLoad: isInitialLoad,
      data: data,
      canNextPage: false,
      nextLink: null,
    );

    emit(state);
    return state;
  }
}
