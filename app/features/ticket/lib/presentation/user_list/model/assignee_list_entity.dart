/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core/models/assignee.dart';
import 'package:gp_shared/domain/entity/entity.dart';

class AssigneeListEntity extends BaseListEntity {
  AssigneeListEntity({
    required this.assignee,
  }) : super(id: assignee.id.toString());

  final Assignee assignee;
}
