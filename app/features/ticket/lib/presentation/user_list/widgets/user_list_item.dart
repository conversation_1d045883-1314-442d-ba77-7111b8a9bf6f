/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class UserListItem extends StatelessWidget {
  const UserListItem({
    required this.user,
    required this.onTap,
    super.key,
  });

  final Assignee user;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundImage: user.avatar?.isNotEmpty == true
                  ? NetworkImage(user.avatar!)
                  : null,
              child: user.avatar?.isEmpty != false
                  ? Text(
                      user.displayName.isNotEmpty
                          ? user.displayName[0].toUpperCase()
                          : '?',
                      style: textStyle(GPTypography.headingMedium)
                          ?.mergeColor(GPColor.functionAlwaysLightPrimary),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            // User info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display name
                  Text(
                    user.displayName,
                    style: textStyle(GPTypography.headingMedium)
                        ?.mergeColor(GPColor.contentPrimary),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  // Department and role
                  if (_getDepartmentAndRole().isNotEmpty)
                    Text(
                      _getDepartmentAndRole(),
                      style: textStyle(GPTypography.bodyMedium)
                          ?.mergeColor(GPColor.contentSecondary),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDepartmentAndRole() {
    final department = user.userDepartment;
    final role = user.userRole;

    if (department?.isNotEmpty == true && role?.isNotEmpty == true) {
      return '$department • $role';
    } else if (department?.isNotEmpty == true) {
      return department!;
    } else if (role?.isNotEmpty == true) {
      return role!;
    }

    // Try to get from work info
    final work = user.info?.work?.firstOrNull;
    if (work != null) {
      final workDept = work.department;
      final workTitle = work.title;

      if (workDept?.isNotEmpty == true && workTitle?.isNotEmpty == true) {
        return '$workDept • $workTitle';
      } else if (workDept?.isNotEmpty == true) {
        return workDept!;
      } else if (workTitle?.isNotEmpty == true) {
        return workTitle!;
      }
    }

    return '';
  }
}
