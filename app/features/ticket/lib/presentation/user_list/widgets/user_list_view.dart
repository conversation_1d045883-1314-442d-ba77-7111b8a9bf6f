/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/base/widgets/base_list_widget.dart';
import 'package:gp_shared/presentation/base/base.dart';

import '../model/assignee_list_entity.dart';
import 'user_list_item.dart';

class UserListView extends GPBaseListViewV2<AssigneeListEntity> {
  UserListView({
    required super.params,
    required this.onUserTap,
    super.key,
  });

  final Function(Assignee) onUserTap;

  @override
  Widget buildItem(
    ListViewMode mode,
    BuildContext context,
    AssigneeListEntity item,
    int index,
  ) {
    return UserListItem(
      user: item.assignee,
      onTap: () => onUserTap(item.assignee),
    );
  }

  @override
  Widget? buildFirstPageProgressIndicator(
    ListViewMode mode,
    BuildContext context,
  ) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  @override
  Widget buildNoItemsFoundIndicator(
    ListViewMode mode,
    BuildContext context,
  ) {
    return const Center(
      child: Text('No users found'),
    );
  }
}
