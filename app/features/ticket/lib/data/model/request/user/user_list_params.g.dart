// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_list_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserListParams _$UserListParamsFromJson(Map<String, dynamic> json) =>
    _UserListParams(
      search: json['search'] as String? ?? '',
      limit: (json['limit'] as num?)?.toInt() ?? 30,
      nextLink: json['nextLink'] as String? ?? '',
      onlyCurrentWorkspace: json['onlyCurrentWorkspace'] as bool? ?? true,
      isSearching: json['isSearching'] as bool? ?? false,
      groupId: json['groupId'] as String? ?? '',
      threadId: json['threadId'] as String? ?? '',
      filterOutMemberIds: (json['filterOutMemberIds'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      ignoreMe: json['ignoreMe'] as bool? ?? false,
    );

Map<String, dynamic> _$UserListParamsToJson(_UserListParams instance) =>
    <String, dynamic>{
      'search': instance.search,
      'limit': instance.limit,
      'nextLink': instance.nextLink,
      'onlyCurrentWorkspace': instance.onlyCurrentWorkspace,
      'isSearching': instance.isSearching,
      'groupId': instance.groupId,
      'threadId': instance.threadId,
      'filterOutMemberIds': instance.filterOutMemberIds,
      'ignoreMe': instance.ignoreMe,
    };
