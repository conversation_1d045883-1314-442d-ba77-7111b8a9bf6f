// ignore_for_file: public_member_api_docs
/*
 * Created Date: 2/01/2024 11:12:45
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 10:57:15
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core/models/comment/comment.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/cancel/ticket_cancel.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/tag/ticket_tag.params.dart';
import 'package:gp_shared/data/data_source/remote/comment/comment.service.dart';
import 'package:injectable/injectable.dart';

import '../../domain/domain.dart';
import '../data.dart';

@LazySingleton(as: TicketRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kTicketRepository')
final class TicketRepositoryImpl implements TicketRepository {
  const TicketRepositoryImpl(
    @Named('kTicketService') this.ticketService,
    @Named('kCommentService') this.commentService,
  );

  final TicketService ticketService;
  final CommentService commentService;

  @override
  Future<ListAPIResponseV2<TicketListResponse>> tickets(
      TicketListParams params) {
    return ticketService.tickets(params: params);
  }

  @override
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets(
    TicketListRelativeParams params,
  ) {
    return ticketService.relativeTickets(params: params);
  }

  @override
  Future<ApiResponseV2<TicketListResponse>> createTickets(
      TicketCreateParams params) {
    return ticketService.createTickets(params: params);
  }

  @override
  Future<ApiResponseV2<TicketListResponse>> details(String ticketId) {
    return ticketService.details(ticketId: ticketId);
  }

  @override
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    required String ticketId,
  }) {
    return ticketService.flowCharts(ticketId: ticketId);
  }

  @override
  Future<ApiResponseV2<TicketNodeResponse>> nodes({
    required String ticketId,
    required String nodeId,
  }) {
    return ticketService.nodes(
      ticketId: ticketId,
      nodeId: nodeId,
    );
  }

  @override
  Future<ApiResponseV2> assignee({
    required TicketUpdateAssigneeInput assigneeInput,
  }) {
    return ticketService.assignee(
      ticketId: assigneeInput.ticketId,
      nodeId: assigneeInput.nodeId,
      params: assigneeInput.params,
    );
  }

  @override
  Future<ApiResponseV2> follower({
    required TicketAddFollowerInput followerInput,
  }) {
    return ticketService.followers(
      ticketId: followerInput.ticketId,
      nodeId: followerInput.nodeId,
      params: followerInput.params,
    );
  }

  @override
  Future<void> addFollowerAllStep({
    required TicketAddFollowerInput followerInput,
  }) {
    return ticketService.addFollowersAllStep(
      ticketId: followerInput.ticketId,
      params: followerInput.params,
    );
  }

  @override
  Future<ApiResponseV2> additionalRequests({
    required TicketAdditionalRequestsParams params,
  }) {
    return ticketService.additionalRequests(
      ticketId: params.ticketId.toString(),
      nodeId: params.nodeId.toString(),
      params: params,
    );
  }

  @override
  Future<void> fields({
    required String ticketId,
    required TicketUpdateFieldValuesParams params,
  }) {
    return ticketService.fields(
      ticketId: ticketId,
      params: params,
    );
  }

  @override
  Future<ApiResponseV2<TicketNodeResponse>> status({
    required String ticketId,
    required String nodeId,
    required TicketUpdateNodeStatusParams params,
  }) {
    return ticketService.status(
      ticketId: ticketId,
      nodeId: nodeId,
      params: params,
    );
  }

  @override
  Future<ApiResponseV2<dynamic>> spam({
    required String ticketId,
    required String nodeId,
    required TicketSpamParams params,
  }) {
    return ticketService.spam(
      ticketId: ticketId,
      nodeId: nodeId,
      params: params,
    );
  }

  @override
  Future<ApiResponseV2> moveToOnHold({
    required String ticketId,
    required String nodeId,
    required TicketMoveToOnHoldParams params,
  }) {
    return ticketService.moveToOnHold(
      ticketId: ticketId,
      nodeId: nodeId,
      params: params,
    );
  }

  @override
  Future<void> acceptOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  }) {
    return ticketService.acceptOnHold(
      ticketId: ticketId,
      nodeId: nodeId,
      onHoldId: onHoldId,
    );
  }

  @override
  Future<ListAPIResponseV2<TicketCommentResponse>> comments(
      {required String ticketId,
      required String nodeId,
      required TicketCommentsParams params}) {
    return ticketService.comments(
        ticketId: ticketId, nodeId: nodeId, params: params);
  }

  @override
  Future<ApiResponseV2<TicketCommentResponse>> postComment({
    required TicketPostCommentsRequestParams params,
  }) {
    return ticketService.postComment(params: params);
  }

  @override
  Future<ApiResponseV2<Comment>> editComment({
    required TicketEditCommentsRequestParams params,
    required String commentId,
  }) {
    return commentService.editComment(
      comment: params.toJson(),
      commentId: commentId,
    );
  }

  @override
  Future<ApiResponseV2WithoutData> deleteComment({
    required String commentId,
  }) {
    return commentService.deleteComment(
      commentId: commentId,
    );
  }

  @override
  Future<ListAPIResponseV2<TicketOnHoldRequestResponse>> getOnHoldRequest({
    required String ticketId,
    required String nodeId,
  }) {
    return ticketService.getOnHoldRequest(
      ticketId: ticketId,
      nodeId: nodeId,
    );
  }

  @override
  Future<void> rejectOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  }) {
    return ticketService.rejectOnHold(
      ticketId: ticketId,
      nodeId: nodeId,
      onHoldId: onHoldId,
    );
  }

  @override
  Future<void> cancelOnHold({
    required String ticketId,
    required String nodeId,
    required String onHoldId,
  }) {
    return ticketService.cancelOnHold(
      ticketId: ticketId,
      nodeId: nodeId,
      onHoldId: onHoldId,
    );
  }

  @override
  Future<ListAPIResponseV2<TicketAdditionalRequestResponse>>
      getAdditionalRequest({
    required String ticketId,
    required String nodeId,
  }) {
    return ticketService.getAdditionalRequest(
      ticketId: ticketId,
      nodeId: nodeId,
    );
  }

  @override
  Future<ListAPIResponseV2<TicketActivityResponse>> getActivities(
      {required String ticketId}) {
    return ticketService.getActivities(ticketId: ticketId);
  }

  @override
  Future<ListAPIResponse<Assignee>> members(Map<String, dynamic> queries) {
    return ticketService.members(membersParams: queries);
  }

  @override
  Future<ListAPIResponse<Assignee>> getFollowMembers({
    required String ticketId,
    required TicketFollowMembersParams params,
  }) {
    return ticketService.getFollowMembers(ticketId: ticketId, params: params);
  }

  @override
  Future<ApiResponseV2<TicketAdditionalRequestResponse>>
      responseAdditionalRequests({
    required String ticketId,
    required String nodeId,
    required String additionalRequestId,
    required TicketResponseAdditionalRequestParams params,
  }) {
    return ticketService.responseAdditionalRequests(
      ticketId: ticketId,
      nodeId: nodeId,
      additionalRequestId: additionalRequestId,
      params: params,
    );
  }

  @override
  Future<void> unfollow({
    required String ticketId,
    required String nodeId,
  }) {
    return ticketService.unfollow(
      ticketId: ticketId,
      nodeId: nodeId,
    );
  }

  @override
  Future<ApiResponseV2<TicketActivityResponse>> getActivityDetail(
      {required String ticketId, required String activityId}) {
    return ticketService.getActivityDetail(
      ticketId: ticketId,
      activityId: activityId,
    );
  }

  @override
  Future<void> deleteTicket({
    required String ticketId,
  }) {
    return ticketService.deleteTicket(ticketId: ticketId);
  }

  @override
  Future<void> reopen({
    required String ticketId,
    required TicketReopenParams params,
  }) {
    return ticketService.reopen(ticketId: ticketId, params: params);
  }

  @override
  Future<void> close({
    required String ticketId,
    required bool hasReview,
  }) {
    final params = <String, dynamic>{};
    if (hasReview) {
      params['parallel_with_reviewing'] = true;
    }
    return ticketService.close(ticketId: ticketId, params: params);
  }

  @override
  Future<ApiResponseV2<TicketListResponse>> review({
    required String ticketId,
    required TicketReviewParams params,
  }) {
    return ticketService.review(ticketId: ticketId, params: params);
  }

  @override
  Future<ApiResponseV2<TicketSpamRequestResponse>> getSpamDetail(
      {required String ticketId,
      required String nodeId,
      required String spamId}) {
    return ticketService.getSpam(
      ticketId: ticketId,
      nodeId: nodeId,
      id: spamId,
    );
  }

  @override
  Future<void> cancel({
    required String ticketId,
    required TicketCancelParams params,
  }) {
    return ticketService.cancel(ticketId: ticketId, params: params);
  }

  @override
  Future<void> labels(
      {required String ticketId,
      required String nodeId,
      required TicketLabelParams params}) {
    return ticketService.labels(
        ticketId: ticketId, nodeId: nodeId, params: params);
  }

  @override
  Future<ApiResponseV2<TicketIsTicketAssigneeResponse>> isAssignee(
      {required String ticketId}) {
    return ticketService.isAssignee(ticketId: ticketId);
  }

  @override
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoNodes({
    required String ticketId,
    required String nodeId,
  }) {
    return ticketService.getCanRedoNodes(
      ticketId: ticketId,
      nodeId: nodeId,
    );
  }

  @override
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoEndNode({
    required String ticketId,
  }) {
    return ticketService.getCanRedoEndNode(
      ticketId: ticketId,
    );
  }

  @override
  Future<void> redoEndNodes({
    required String ticketId,
    required TicketRedoEndNodeParams params,
  }) {
    return ticketService.redoEndNodes(
      ticketId: ticketId,
      params: params,
    );
  }

  @override
  Future<void> redoPrevious({
    required String ticketId,
    required String nodeId,
    required TicketRedoPreviousParams params,
  }) {
    return ticketService.redoPrevious(
      ticketId: ticketId,
      nodeId: nodeId,
      params: params,
    );
  }

  @override
  Future<ListAPIResponse<Assignee>> getNodeAssignees({
    required String ticketId,
    required String nodeId,
    required Map<String, dynamic> params,
  }) {
    return ticketService.getNodeAssignees(
      ticketId: ticketId,
      nodeId: nodeId,
      params: params,
    );
  }
}
