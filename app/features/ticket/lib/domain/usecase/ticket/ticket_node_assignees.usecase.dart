// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketNodeAssigneesUseCase extends GPBaseFutureUseCase<
    TicketNodeAssigneesInput, TicketNodeAssigneesOutput> {
  TicketNodeAssigneesUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<TicketNodeAssigneesOutput> buildUseCase(
    TicketNodeAssigneesInput input,
  ) async {
    final params = <String, dynamic>{
      'limit': input.limit,
    };

    if (input.search != null && input.search!.isNotEmpty) {
      params['name'] = input.search;
    }

    final response = await _ticketRepository.getNodeAssignees(
      ticketId: input.ticketId,
      nodeId: input.nodeId,
      params: params,
    );

    return TicketNodeAssigneesOutput(response.data ?? []);
  }
}
