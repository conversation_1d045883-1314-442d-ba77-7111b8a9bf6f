/*
 * Created Date: Tuesday, 23rd July 2024, 08:50:53
 * Author: gapo
 * -----
 * Last Modified: Tuesday, 23rd July 2024 08:51:30
 * Modified By: gapo
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core/extensions/date_time_extension.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_additional_request.entity.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_onhold_request_respose.entity.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_reopen_request_entity.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:flutter/material.dart';

import 'ticket_creator.entity.dart';
import 'ticket_activity_content.entity.dart';

enum TicketActivityType {
  created(1),
  edited(2),
  updatedFollowers(3),
  commented(4),
  reopened(5),
  providedAdditionalInformation(6),
  rated(7),
  cancelled(8),
  closed(9),
  assigned(10),
  autoAssigned(11),
  completed(12),
  approved(13),
  denied(14),
  reassigned(15),
  requestedAdditionalInformation(16),
  onHold(17),
  approvedOnHold(18),
  deniedOnHold(19),
  resumedProcessing(20),
  unfollowed(21),
  createdOnHoldRequest(22),
  autoClosed(23),
  reportSpam(24),
  internalNote(25),
  nodeUpdateTag(26),
  resumedProcessingAfterCancelledAllWaitingAdditionalRequests(27),
  previousStep(28),
  previousMultiStep(29),
  updatedFollowersAllStep(30);

  final int value;
  const TicketActivityType(this.value);

  static TicketActivityType? fromValue(int value) {
    return TicketActivityType.values.firstWhereOrNull(
      (type) => type.value == value,
    );
  }

  String get iconName {
    switch (this) {
      case TicketActivityType.created:
      case TicketActivityType.edited:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_TICKET_SVG;
      case TicketActivityType.updatedFollowers:
      case TicketActivityType.unfollowed:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_EYE_SVG;
      case TicketActivityType.commented:
      case TicketActivityType.internalNote:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_2_BUBBLE_SVG;
      case TicketActivityType.reopened:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_DOOR_SVG;
      case TicketActivityType.providedAdditionalInformation:
      case TicketActivityType.requestedAdditionalInformation:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_FILE_SVG;
      case TicketActivityType.rated:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_STAR_SVG;
      case TicketActivityType.cancelled:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_XMARK_CIRCLE_SVG;
      case TicketActivityType.closed:
      case TicketActivityType.autoClosed:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_CHECKMARK_CIRCLE_SVG;
      case TicketActivityType.assigned:
      case TicketActivityType.autoAssigned:
      case TicketActivityType.reassigned:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_CIRCLE_SVG;
      case TicketActivityType.completed:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_CIRCLE_SVG;
      case TicketActivityType.approved:
      case TicketActivityType.approvedOnHold:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_CIRCLE_SVG;
      case TicketActivityType.denied:
      case TicketActivityType.deniedOnHold:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_CHECKMARK_CIRCLE_SVG;
      case TicketActivityType.onHold:
      case TicketActivityType.createdOnHoldRequest:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PAUSE_SVG;
      case TicketActivityType.resumedProcessing:
      case TicketActivityType
            .resumedProcessingAfterCancelledAllWaitingAdditionalRequests:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_ARROW_HOOK_RIGHT_REDO_SVG;
      case TicketActivityType.reportSpam:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_WARNINGMARK_SVG;
      case TicketActivityType.nodeUpdateTag:
        return Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_FILE_SVG;
      case TicketActivityType.previousStep:
      case TicketActivityType.previousMultiStep:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ARROW_HOOK_LEFT_UNDO_SVG;
      case TicketActivityType.updatedFollowersAllStep:
        return Assets
            .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_ADD_SVG;
    }
  }

  Color get iconColor {
    switch (this) {
      case TicketActivityType.reportSpam:
      case TicketActivityType.denied:
      case TicketActivityType.deniedOnHold:
      case TicketActivityType.cancelled:
        return GPColor.functionNegativePrimary;
      case TicketActivityType.closed:
      case TicketActivityType.autoClosed:
        return GPColor.functionAccentWorkSecondary;
      default:
        return GPColor.contentSecondary;
    }
  }
}

final class TicketActivityEntity extends BaseListEntity {
  TicketActivityEntity({
    super.id,
    required this.workspaceId,
    required this.ticketId,
    required this.workflowId,
    required this.actorId,
    required this.actorType,
    required this.type,
    required this.targetId,
    required this.objectId,
    this.actor,
    this.createdAt,
    this.content,
    this.additionalRequest,
    this.onHoldRequest,
    this.reopenRequest,
    this.reason,
  });

  final String workspaceId;

  final int ticketId;

  final int workflowId;

  final String actorId;

  final int actorType;

  final int type;

  final String targetId;
  final String objectId;
  final TicketCreatorEntity? actor;
  final DateTime? createdAt;
  final TicketActivityContentEntity? content;
  final TicketAdditionalRequestEntity? additionalRequest;
  final TicketOnHoldRequestEntity? onHoldRequest;
  final TicketReopenRequestEntity? reopenRequest;
  final String? reason;

  bool get hasDetailButton => [
        TicketActivityType.reopened,
        TicketActivityType.requestedAdditionalInformation,
        TicketActivityType.createdOnHoldRequest,
        TicketActivityType.reportSpam,
        TicketActivityType.previousStep,
        TicketActivityType.previousMultiStep,
      ].map((type) => type.value).contains(type);

  String popupTitle() {
    final activityType = TicketActivityType.fromValue(type);
    switch (activityType) {
      case TicketActivityType.reopened:
        return LocaleKeys.ticket_activity_type_with_detail_reopen_title.tr;
      case TicketActivityType.requestedAdditionalInformation:
        return LocaleKeys.ticket_activity_type_with_detail_additional_title.tr;
      case TicketActivityType.createdOnHoldRequest:
        return LocaleKeys.ticket_activity_type_with_detail_onhold_title.tr;
      case TicketActivityType.reportSpam:
        return LocaleKeys.ticket_activity_type_with_detail_spam_title.tr;
      case TicketActivityType.previousStep:
      case TicketActivityType.previousMultiStep:
        return LocaleKeys.ticket_activity_type_with_detail_back_step.tr;
      default:
        return '';
    }
  }

  String popupReason() {
    final activityType = TicketActivityType.fromValue(type);
    switch (activityType) {
      case TicketActivityType.reopened:
        return reopenRequest?.reason ?? '';
      case TicketActivityType.requestedAdditionalInformation:
        return additionalRequest?.contentNeedAdd ?? '';
      case TicketActivityType.createdOnHoldRequest:
        return onHoldRequest?.reason ?? '';
      default:
        return reason ?? '';
    }
  }

  String createdTimeString() {
    if (createdAt == null) return '';
    return createdAt!.formatHHmmddMMyyyy;
  }

  Widget? getActivityIcon() {
    final activityType = TicketActivityType.fromValue(type);
    return SvgWidget(
      activityType?.iconName ?? '',
      color: activityType?.iconColor,
    );
  }
}
