/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core_v2/gp_core_v2.dart';

part 'ticket_node_assignees.input.freezed.dart';
part 'ticket_node_assignees.input.g.dart';

@freezed
class TicketNodeAssigneesInput extends GPBaseInput
    with _$TicketNodeAssigneesInput {
  const TicketNodeAssigneesInput._();

  const factory TicketNodeAssigneesInput({
    required String ticketId,
    required String nodeId,
    String? search,
    @Default(30) int limit,
  }) = _TicketNodeAssigneesInput;

  factory TicketNodeAssigneesInput.fromJson(Map<String, dynamic> json) =>
      _$TicketNodeAssigneesInputFromJson(json);
}
