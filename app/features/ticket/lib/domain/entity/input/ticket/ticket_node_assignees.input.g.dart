// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_node_assignees.input.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TicketNodeAssigneesInput _$TicketNodeAssigneesInputFromJson(
        Map<String, dynamic> json) =>
    _TicketNodeAssigneesInput(
      ticketId: json['ticketId'] as String,
      nodeId: json['nodeId'] as String,
      search: json['search'] as String?,
      limit: (json['limit'] as num?)?.toInt() ?? 30,
    );

Map<String, dynamic> _$TicketNodeAssigneesInputToJson(
        _TicketNodeAssigneesInput instance) =>
    <String, dynamic>{
      'ticketId': instance.ticketId,
      'nodeId': instance.nodeId,
      'search': instance.search,
      'limit': instance.limit,
    };
