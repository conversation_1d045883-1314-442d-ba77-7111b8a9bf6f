// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_node_assignees.input.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketNodeAssigneesInput {
  String get ticketId;
  String get nodeId;
  String? get search;
  int get limit;

  /// Create a copy of TicketNodeAssigneesInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketNodeAssigneesInputCopyWith<TicketNodeAssigneesInput> get copyWith =>
      _$TicketNodeAssigneesInputCopyWithImpl<TicketNodeAssigneesInput>(
          this as TicketNodeAssigneesInput, _$identity);

  /// Serializes this TicketNodeAssigneesInput to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketNodeAssigneesInput &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.limit, limit) || other.limit == limit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, ticketId, nodeId, search, limit);

  @override
  String toString() {
    return 'TicketNodeAssigneesInput(ticketId: $ticketId, nodeId: $nodeId, search: $search, limit: $limit)';
  }
}

/// @nodoc
abstract mixin class $TicketNodeAssigneesInputCopyWith<$Res> {
  factory $TicketNodeAssigneesInputCopyWith(TicketNodeAssigneesInput value,
          $Res Function(TicketNodeAssigneesInput) _then) =
      _$TicketNodeAssigneesInputCopyWithImpl;
  @useResult
  $Res call({String ticketId, String nodeId, String? search, int limit});
}

/// @nodoc
class _$TicketNodeAssigneesInputCopyWithImpl<$Res>
    implements $TicketNodeAssigneesInputCopyWith<$Res> {
  _$TicketNodeAssigneesInputCopyWithImpl(this._self, this._then);

  final TicketNodeAssigneesInput _self;
  final $Res Function(TicketNodeAssigneesInput) _then;

  /// Create a copy of TicketNodeAssigneesInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ticketId = null,
    Object? nodeId = null,
    Object? search = freezed,
    Object? limit = null,
  }) {
    return _then(_self.copyWith(
      ticketId: null == ticketId
          ? _self.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String,
      nodeId: null == nodeId
          ? _self.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as String,
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TicketNodeAssigneesInput extends TicketNodeAssigneesInput {
  const _TicketNodeAssigneesInput(
      {required this.ticketId,
      required this.nodeId,
      this.search,
      this.limit = 30})
      : super._();
  factory _TicketNodeAssigneesInput.fromJson(Map<String, dynamic> json) =>
      _$TicketNodeAssigneesInputFromJson(json);

  @override
  final String ticketId;
  @override
  final String nodeId;
  @override
  final String? search;
  @override
  @JsonKey()
  final int limit;

  /// Create a copy of TicketNodeAssigneesInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketNodeAssigneesInputCopyWith<_TicketNodeAssigneesInput> get copyWith =>
      __$TicketNodeAssigneesInputCopyWithImpl<_TicketNodeAssigneesInput>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketNodeAssigneesInputToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketNodeAssigneesInput &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.search, search) || other.search == search) &&
            (identical(other.limit, limit) || other.limit == limit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, ticketId, nodeId, search, limit);

  @override
  String toString() {
    return 'TicketNodeAssigneesInput(ticketId: $ticketId, nodeId: $nodeId, search: $search, limit: $limit)';
  }
}

/// @nodoc
abstract mixin class _$TicketNodeAssigneesInputCopyWith<$Res>
    implements $TicketNodeAssigneesInputCopyWith<$Res> {
  factory _$TicketNodeAssigneesInputCopyWith(_TicketNodeAssigneesInput value,
          $Res Function(_TicketNodeAssigneesInput) _then) =
      __$TicketNodeAssigneesInputCopyWithImpl;
  @override
  @useResult
  $Res call({String ticketId, String nodeId, String? search, int limit});
}

/// @nodoc
class __$TicketNodeAssigneesInputCopyWithImpl<$Res>
    implements _$TicketNodeAssigneesInputCopyWith<$Res> {
  __$TicketNodeAssigneesInputCopyWithImpl(this._self, this._then);

  final _TicketNodeAssigneesInput _self;
  final $Res Function(_TicketNodeAssigneesInput) _then;

  /// Create a copy of TicketNodeAssigneesInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? ticketId = null,
    Object? nodeId = null,
    Object? search = freezed,
    Object? limit = null,
  }) {
    return _then(_TicketNodeAssigneesInput(
      ticketId: null == ticketId
          ? _self.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String,
      nodeId: null == nodeId
          ? _self.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as String,
      search: freezed == search
          ? _self.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
