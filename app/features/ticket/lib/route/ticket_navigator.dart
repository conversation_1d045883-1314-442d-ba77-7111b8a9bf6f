import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:injectable/injectable.dart';
import 'package:gp_feat_ticket/data/model/response/ticket/ticket_additional_request_response.dart';
import 'package:gp_feat_ticket/data/model/response/ticket/ticket_list_response.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';

import '../domain/entity/ticket/ticket_list.entity.dart';
import '../presentation/create/ticket_create.page.dart';
import 'arguments/page_arguments.dart';
import 'router_name.dart';

@LazySingleton()
class TicketNavigator {
  Future<dynamic> toTicketDetails({
    required String id,
    TicketEntity? entity,
  }) async {
    return Get.toNamed(
      TicketRoutes.ticketDetails,
      arguments: TicketDetailsArguments(
        id: id,
        entity: entity,
      ),
    );
  }

  Future<dynamic> toCreateTicket({
    TicketCreateMode mode = TicketCreateMode.create,
    dynamic ticketListResponse,
  }) async {
    return Get.toNamed(
      TicketRoutes.ticketCreate,
      arguments: TicketCreateArguments(
        mode: mode,
        ticketListResponse: ticketListResponse,
      ),
    );
  }

  Future<dynamic> toEditTicket({
    required TicketListResponse ticketListResponse,
    List<WorkflowFormFieldPermissionEntity>? permissions,
    required TicketNodeEntity nodeEntity,
  }) async {
    return Get.toNamed(
      TicketRoutes.ticketEdit,
      arguments: TicketEditArguments(
        ticketListResponse: ticketListResponse,
        permissions: permissions,
        nodeEntity: nodeEntity,
      ),
    );
  }

  Future<dynamic> toAdditionalRequestEditTicket({
    required TicketListResponse ticketListResponse,
    required TicketAdditionalRequestResponse additionalRequestResponse,
    List<WorkflowFormFieldPermissionEntity>? fieldPermissions,
    required TicketNodeEntity ticketNodeEntity,
  }) async {
    return Get.toNamed(
      TicketRoutes.ticketAdditionalRequestEdit,
      arguments: TicketAdditionalRequestEditArguments(
        ticketListResponse: ticketListResponse,
        additionalRequestResponse: additionalRequestResponse,
        fieldPermissions: fieldPermissions,
        ticketNodeEntity: ticketNodeEntity,
      ),
    );
  }

  Future<Assignee?> toUserList({
    String? title,
    List<int> filterOutMemberIds = const [],
    bool ignoreMe = false,
    String? ticketId,
    String? nodeId,
    bool useNodeAssigneesAPI = false,
  }) async {
    Assignee? selectedUser;

    await Get.toNamed(
      TicketRoutes.userList,
      arguments: UserListArguments(
        title: title,
        filterOutMemberIds: filterOutMemberIds,
        ignoreMe: ignoreMe,
        ticketId: ticketId,
        nodeId: nodeId,
        useNodeAssigneesAPI: useNodeAssigneesAPI,
        onUserSelected: (user) {
          selectedUser = user;
        },
      ),
    );

    return selectedUser;
  }

  Future<dynamic> showBottomSheet({
    required Widget child,
    bool isScrollControlled = true,
    bool isDismissible = true,
  }) async {
    return Popup.instance.showBottomSheet(
      child,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
    );
  }
}
